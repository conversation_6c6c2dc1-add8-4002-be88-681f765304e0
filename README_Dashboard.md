# BNO055 Sensor Dashboard

A real-time dashboard for visualizing data from the XIAO nRF52840 BNO055 sensor using Plotly Dash.

## Features

- **Real-time data visualization** of all BNO055 sensor readings
- **Multiple sensor views**:
  - Orientation (Euler angles: roll, pitch, yaw)
  - Gyroscope data (angular velocity)
  - Accelerometer data
  - Magnetometer data
  - Linear acceleration (without gravity)
  - Gravity vector
  - Temperature
  - Calibration status
- **Automatic Arduino detection** or manual port specification
- **Interactive graphs** with zoom, pan, and hover features
- **Live updates** at 10Hz (configurable)

## Setup

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Upload Arduino Code

Make sure your XIAO nRF52840 is running the `XIAO_nRF52840_BNO055.ino` code and is connected via USB.

### 3. Run the Dashboard

```bash
python sensor_dashboard.py
```

The dashboard will be available at: http://127.0.0.1:8050

## Usage

1. **Connect to Arduino**:
   - Leave the port field empty for auto-detection, or
   - Enter the specific COM port (e.g., `COM3` on Windows, `/dev/ttyUSB0` on Linux)
   - Click "Connect"

2. **View Real-time Data**:
   - The dashboard will automatically start displaying sensor data
   - All graphs update in real-time
   - Use mouse to zoom, pan, and interact with graphs

3. **Monitor Calibration**:
   - Check the calibration status graph
   - Values of 3 indicate full calibration for each sensor
   - Move the sensor in figure-8 patterns to calibrate magnetometer
   - Rotate around all axes to calibrate gyroscope

## Graph Descriptions

### Orientation (Euler Angles)
- **X (Roll)**: Rotation around X-axis (-180° to +180°)
- **Y (Pitch)**: Rotation around Y-axis (-90° to +90°)
- **Z (Yaw)**: Rotation around Z-axis (0° to 360°)

### Gyroscope
- Angular velocity in rad/s around X, Y, Z axes

### Accelerometer
- Linear acceleration in m/s² including gravity

### Magnetometer
- Magnetic field strength in µT (microtesla)

### Linear Acceleration
- Acceleration without gravity component

### Gravity Vector
- Gravity component isolated from total acceleration

### Temperature
- Sensor temperature in °C

### Calibration Status
- System, Gyro, Accel, Mag calibration levels (0-3)
- 3 = fully calibrated, 0 = not calibrated

## Troubleshooting

### Arduino Not Detected
- Check USB connection
- Verify Arduino is running the BNO055 code
- Try specifying the port manually
- On Windows, check Device Manager for COM ports
- On Linux/Mac, check `/dev/tty*` devices

### No Data Appearing
- Ensure Arduino Serial Monitor shows CSV data output
- Check baud rate (should be 115200)
- Verify BNO055 sensor is properly connected and calibrated

### Performance Issues
- Reduce `MAX_DATA_POINTS` in the code for lower memory usage
- Increase `UPDATE_INTERVAL` for slower updates

## Configuration

You can modify these parameters in `sensor_dashboard.py`:

```python
MAX_DATA_POINTS = 500    # Number of data points to keep in memory
UPDATE_INTERVAL = 100    # Dashboard update interval in milliseconds
SERIAL_TIMEOUT = 1       # Serial read timeout in seconds
```

## Data Format

The Arduino sends CSV data with the following columns:
```
Timestamp,Euler_X,Euler_Y,Euler_Z,Gyro_X,Gyro_Y,Gyro_Z,Accel_X,Accel_Y,Accel_Z,Mag_X,Mag_Y,Mag_Z,LinAccel_X,LinAccel_Y,LinAccel_Z,Gravity_X,Gravity_Y,Gravity_Z,Temperature,System_Cal,Gyro_Cal,Accel_Cal,Mag_Cal
```

## Applications

This dashboard is perfect for:
- **Motion analysis** and sports applications
- **Robotics** orientation monitoring
- **IoT projects** requiring orientation sensing
- **Educational** demonstrations of IMU sensors
- **Calibration** and sensor validation
- **Data logging** and analysis

## License

This project is open source and available under the MIT License.
