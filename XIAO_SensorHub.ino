/*
 * XIAO-SensorHub Arduino Sketch
 * Reads IMU sensor data and transmits via Bluetooth Low Energy (BLE)
 * Compatible with XIAO ESP32C3 or similar boards with IMU sensors
 */

#include <ArduinoBLE.h>
#include <Arduino_LSM6DS3.h>  // For IMU sensor (adjust based on your sensor)
#include <math.h>

// BLE Configuration
#define DEVICE_NAME "XIAO-SensorHub"
#define SERVICE_UUID "12345678-1234-1234-1234-123456789abc"
#define CHARACTERISTIC_UUID "*************-4321-4321-cba987654321"

// BLE Service and Characteristic
BLEService sensorService(SERVICE_UUID);
BLECharacteristic sensorCharacteristic(CHARACTERISTIC_UUID, BLERead | BLENotify, 20);

// Sensor data variables
float accelX, accelY, accelZ;
float gyroX, gyroY, gyroZ;
float roll, pitch, yaw = 0;
unsigned long lastTime = 0;
unsigned long currentTime = 0;

// Data packet structure (20 bytes total)
struct SensorData {
  uint8_t header1 = 0xAA;
  uint8_t header2 = 0xAA;
  uint32_t timestamp;
  int16_t angular_velocity;
  int16_t angular_velocity_x;
  int16_t angular_velocity_y;
  int16_t angular_velocity_z;
  int16_t accel_x;
  int16_t accel_y;
  int16_t accel_z;
} __attribute__((packed));

// LED pin for status indication
#define LED_PIN LED_BUILTIN

// Function prototypes
void setupBLE();
void readSensorData();
void calculateOrientation();
void transmitData();
void blinkLED(int times, int delayMs);

void setup() {
  Serial.begin(115200);
  while (!Serial) delay(10);
  
  Serial.println("🚀 XIAO-SensorHub Starting...");
  
  // Initialize LED
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);
  
  // Initialize IMU
  if (!IMU.begin()) {
    Serial.println("❌ Failed to initialize IMU!");
    while (1) {
      blinkLED(3, 200);
      delay(1000);
    }
  }
  Serial.println("✅ IMU initialized successfully");
  
  // Setup BLE
  setupBLE();
  
  Serial.println("✅ Setup complete - Ready to transmit sensor data");
  blinkLED(5, 100);
}

void loop() {
  // Check BLE connection
  BLEDevice central = BLE.central();
  
  if (central) {
    Serial.print("🔗 Connected to central: ");
    Serial.println(central.address());
    digitalWrite(LED_PIN, HIGH);
    
    while (central.connected()) {
      readSensorData();
      calculateOrientation();
      transmitData();
      delay(100); // 10Hz update rate
    }
    
    Serial.println("🔌 Disconnected from central");
    digitalWrite(LED_PIN, LOW);
  }
  
  delay(100);
}

void setupBLE() {
  if (!BLE.begin()) {
    Serial.println("❌ Starting BLE failed!");
    while (1) {
      blinkLED(2, 500);
      delay(1000);
    }
  }
  
  // Set BLE name and service
  BLE.setLocalName(DEVICE_NAME);
  BLE.setAdvertisedService(sensorService);
  
  // Add characteristic to service
  sensorService.addCharacteristic(sensorCharacteristic);
  
  // Add service
  BLE.addService(sensorService);
  
  // Start advertising
  BLE.advertise();
  
  Serial.println("✅ BLE setup complete");
  Serial.print("📡 Advertising as: ");
  Serial.println(DEVICE_NAME);
}

void readSensorData() {
  // Read accelerometer data
  if (IMU.accelerationAvailable()) {
    IMU.readAcceleration(accelX, accelY, accelZ);
  }
  
  // Read gyroscope data
  if (IMU.gyroscopeAvailable()) {
    IMU.readGyroscope(gyroX, gyroY, gyroZ);
  }
  
  currentTime = millis();
}

void calculateOrientation() {
  // Calculate roll and pitch from accelerometer data
  if (abs(accelZ) > 0.1) {
    float newRoll = atan2(accelY, accelZ) * 180.0 / PI;
    float newPitch = atan2(-accelX, accelZ) * 180.0 / PI;
    
    // Apply simple low-pass filter
    roll = 0.1 * newRoll + 0.9 * roll;
    pitch = 0.1 * newPitch + 0.9 * pitch;
  }
  
  // Integrate yaw from gyroscope Z-axis (simple integration)
  if (lastTime > 0) {
    float dt = (currentTime - lastTime) / 1000.0; // Convert to seconds
    yaw += gyroZ * dt;
  }
  lastTime = currentTime;
}

void transmitData() {
  SensorData data;
  
  // Fill timestamp
  data.timestamp = currentTime;
  
  // Calculate total angular velocity magnitude
  float angularVelocityMagnitude = sqrt(gyroX*gyroX + gyroY*gyroY + gyroZ*gyroZ);
  
  // Convert float values to int16 (multiply by 100 for precision)
  data.angular_velocity = (int16_t)(angularVelocityMagnitude * 100);
  data.angular_velocity_x = (int16_t)(gyroX * 100);
  data.angular_velocity_y = (int16_t)(gyroY * 100);
  data.angular_velocity_z = (int16_t)(gyroZ * 100);
  data.accel_x = (int16_t)(accelX * 100);
  data.accel_y = (int16_t)(accelY * 100);
  data.accel_z = (int16_t)(accelZ * 100);
  
  // Transmit data via BLE
  sensorCharacteristic.writeValue((uint8_t*)&data, sizeof(data));
  
  // Debug output
  Serial.print("📊 Data: ");
  Serial.print("Accel("); Serial.print(accelX, 2); Serial.print(","); 
  Serial.print(accelY, 2); Serial.print(","); Serial.print(accelZ, 2); Serial.print(") ");
  Serial.print("Gyro("); Serial.print(gyroX, 2); Serial.print(","); 
  Serial.print(gyroY, 2); Serial.print(","); Serial.print(gyroZ, 2); Serial.print(") ");
  Serial.print("Orient("); Serial.print(roll, 1); Serial.print("°,"); 
  Serial.print(pitch, 1); Serial.print("°,"); Serial.print(yaw, 1); Serial.println("°)");
}

void blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}
