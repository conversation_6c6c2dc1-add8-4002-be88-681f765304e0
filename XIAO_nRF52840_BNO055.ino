/*
 * XIAO nRF52840 BNO055 Sensor Reader
 * Reads orientation and motion data from BNO055 sensor
 * Outputs data to serial port for logging/analysis
 * 
 * Hardware:
 * - Seeed XIAO nRF52840 (Sense)
 * - Adafruit BNO055 9-DOF sensor
 * 
 * Connections:
 * BNO055 VIN -> 3.3V
 * BNO055 GND -> GND
 * BNO055 SCL -> A5 (SCL)
 * BNO055 SDA -> A4 (SDA)
 */

#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BNO055.h>
#include <utility/imumaths.h>

// BNO055 sensor instance
Adafruit_BNO055 bno = Adafruit_BNO055(55, 0x28);

// Timing variables
unsigned long lastTime = 0;
unsigned long currentTime = 0;
const unsigned long SAMPLE_RATE = 100; // 100ms = 10Hz

// LED pin for status indication
#define LED_PIN LED_BUILTIN

// Data structure for sensor readings
struct SensorReading {
  unsigned long timestamp;
  float euler_x, euler_y, euler_z;           // Euler angles (roll, pitch, yaw)
  float gyro_x, gyro_y, gyro_z;              // Gyroscope data
  float accel_x, accel_y, accel_z;           // Accelerometer data
  float mag_x, mag_y, mag_z;                 // Magnetometer data
  float linear_accel_x, linear_accel_y, linear_accel_z; // Linear acceleration
  float gravity_x, gravity_y, gravity_z;     // Gravity vector
  int8_t temperature;                        // Temperature
  uint8_t system_cal, gyro_cal, accel_cal, mag_cal; // Calibration status
};

void setup() {
  Serial.begin(115200);
  while (!Serial) delay(10);
  
  Serial.println("🚀 XIAO nRF52840 BNO055 Sensor Reader Starting...");
  
  // Initialize LED
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);
  
  // Initialize I2C
  Wire.begin();
  
  // Initialize BNO055
  if (!bno.begin()) {
    Serial.println("❌ No BNO055 detected. Check wiring or I2C address!");
    while (1) {
      blinkLED(3, 200);
      delay(1000);
    }
  }
  
  Serial.println("✅ BNO055 detected successfully");
  
  // Display sensor details
  sensor_t sensor;
  bno.getSensor(&sensor);
  Serial.println("📋 Sensor Details:");
  Serial.print("   Name: "); Serial.println(sensor.name);
  Serial.print("   Version: "); Serial.println(sensor.version);
  Serial.print("   Unique ID: "); Serial.println(sensor.sensor_id);
  Serial.print("   Max Value: "); Serial.print(sensor.max_value); Serial.println(" xxx");
  Serial.print("   Min Value: "); Serial.print(sensor.min_value); Serial.println(" xxx");
  Serial.print("   Resolution: "); Serial.print(sensor.resolution); Serial.println(" xxx");
  
  // Set to NDOF mode for full sensor fusion
  bno.setMode(OPERATION_MODE_NDOF);
  
  delay(1000);
  
  // Print CSV header
  printCSVHeader();
  
  Serial.println("✅ Setup complete - Starting data collection");
  blinkLED(5, 100);
  
  lastTime = millis();
}

void loop() {
  currentTime = millis();
  
  // Check if it's time for a new reading
  if (currentTime - lastTime >= SAMPLE_RATE) {
    SensorReading reading;
    
    if (readSensorData(&reading)) {
      outputData(&reading);
      digitalWrite(LED_PIN, !digitalRead(LED_PIN)); // Toggle LED
    } else {
      Serial.println("❌ Failed to read sensor data");
    }
    
    lastTime = currentTime;
  }
  
  delay(10); // Small delay to prevent overwhelming the loop
}

bool readSensorData(SensorReading* reading) {
  reading->timestamp = currentTime;
  
  // Read Euler angles (orientation)
  imu::Vector<3> euler = bno.getVector(Adafruit_BNO055::VECTOR_EULER);
  reading->euler_x = euler.x();
  reading->euler_y = euler.y();
  reading->euler_z = euler.z();
  
  // Read gyroscope data
  imu::Vector<3> gyro = bno.getVector(Adafruit_BNO055::VECTOR_GYROSCOPE);
  reading->gyro_x = gyro.x();
  reading->gyro_y = gyro.y();
  reading->gyro_z = gyro.z();
  
  // Read accelerometer data
  imu::Vector<3> accel = bno.getVector(Adafruit_BNO055::VECTOR_ACCELEROMETER);
  reading->accel_x = accel.x();
  reading->accel_y = accel.y();
  reading->accel_z = accel.z();
  
  // Read magnetometer data
  imu::Vector<3> mag = bno.getVector(Adafruit_BNO055::VECTOR_MAGNETOMETER);
  reading->mag_x = mag.x();
  reading->mag_y = mag.y();
  reading->mag_z = mag.z();
  
  // Read linear acceleration (acceleration without gravity)
  imu::Vector<3> linear_accel = bno.getVector(Adafruit_BNO055::VECTOR_LINEARACCEL);
  reading->linear_accel_x = linear_accel.x();
  reading->linear_accel_y = linear_accel.y();
  reading->linear_accel_z = linear_accel.z();
  
  // Read gravity vector
  imu::Vector<3> gravity = bno.getVector(Adafruit_BNO055::VECTOR_GRAVITY);
  reading->gravity_x = gravity.x();
  reading->gravity_y = gravity.y();
  reading->gravity_z = gravity.z();
  
  // Read temperature
  reading->temperature = bno.getTemp();
  
  // Read calibration status
  bno.getCalibration(&reading->system_cal, &reading->gyro_cal, 
                     &reading->accel_cal, &reading->mag_cal);
  
  return true;
}

void outputData(SensorReading* reading) {
  // Output as CSV format for easy data logging
  Serial.print(reading->timestamp); Serial.print(",");
  
  // Euler angles (degrees)
  Serial.print(reading->euler_x, 3); Serial.print(",");
  Serial.print(reading->euler_y, 3); Serial.print(",");
  Serial.print(reading->euler_z, 3); Serial.print(",");
  
  // Gyroscope (rad/s)
  Serial.print(reading->gyro_x, 3); Serial.print(",");
  Serial.print(reading->gyro_y, 3); Serial.print(",");
  Serial.print(reading->gyro_z, 3); Serial.print(",");
  
  // Accelerometer (m/s²)
  Serial.print(reading->accel_x, 3); Serial.print(",");
  Serial.print(reading->accel_y, 3); Serial.print(",");
  Serial.print(reading->accel_z, 3); Serial.print(",");
  
  // Magnetometer (µT)
  Serial.print(reading->mag_x, 3); Serial.print(",");
  Serial.print(reading->mag_y, 3); Serial.print(",");
  Serial.print(reading->mag_z, 3); Serial.print(",");
  
  // Linear acceleration (m/s²)
  Serial.print(reading->linear_accel_x, 3); Serial.print(",");
  Serial.print(reading->linear_accel_y, 3); Serial.print(",");
  Serial.print(reading->linear_accel_z, 3); Serial.print(",");
  
  // Gravity vector (m/s²)
  Serial.print(reading->gravity_x, 3); Serial.print(",");
  Serial.print(reading->gravity_y, 3); Serial.print(",");
  Serial.print(reading->gravity_z, 3); Serial.print(",");
  
  // Temperature (°C)
  Serial.print(reading->temperature); Serial.print(",");
  
  // Calibration status (0-3, 3 = fully calibrated)
  Serial.print(reading->system_cal); Serial.print(",");
  Serial.print(reading->gyro_cal); Serial.print(",");
  Serial.print(reading->accel_cal); Serial.print(",");
  Serial.print(reading->mag_cal);
  
  Serial.println(); // End line
}

void printCSVHeader() {
  Serial.println("# XIAO nRF52840 BNO055 Sensor Data");
  Serial.println("# Timestamp(ms),Euler_X(deg),Euler_Y(deg),Euler_Z(deg),Gyro_X(rad/s),Gyro_Y(rad/s),Gyro_Z(rad/s),Accel_X(m/s²),Accel_Y(m/s²),Accel_Z(m/s²),Mag_X(µT),Mag_Y(µT),Mag_Z(µT),LinAccel_X(m/s²),LinAccel_Y(m/s²),LinAccel_Z(m/s²),Gravity_X(m/s²),Gravity_Y(m/s²),Gravity_Z(m/s²),Temp(°C),Sys_Cal,Gyro_Cal,Accel_Cal,Mag_Cal");
  Serial.println("Timestamp,Euler_X,Euler_Y,Euler_Z,Gyro_X,Gyro_Y,Gyro_Z,Accel_X,Accel_Y,Accel_Z,Mag_X,Mag_Y,Mag_Z,LinAccel_X,LinAccel_Y,LinAccel_Z,Gravity_X,Gravity_Y,Gravity_Z,Temperature,System_Cal,Gyro_Cal,Accel_Cal,Mag_Cal");
}

void blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}

void printCalibrationStatus() {
  uint8_t system, gyro, accel, mag;
  bno.getCalibration(&system, &gyro, &accel, &mag);
  
  Serial.print("📊 Calibration Status - Sys:");
  Serial.print(system);
  Serial.print(" Gyro:");
  Serial.print(gyro);
  Serial.print(" Accel:");
  Serial.print(accel);
  Serial.print(" Mag:");
  Serial.println(mag);
}
