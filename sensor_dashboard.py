#!/usr/bin/env python3
"""
BNO055 Sensor Dashboard
Real-time visualization of XIAO nRF52840 BNO055 sensor data using Plotly Dash
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objs as go
import plotly.express as px
import pandas as pd
import serial
import serial.tools.list_ports
import threading
import time
import queue
import numpy as np
from collections import deque
import json

# Configuration
MAX_DATA_POINTS = 500  # Maximum number of data points to keep in memory
UPDATE_INTERVAL = 100  # Dashboard update interval in milliseconds
SERIAL_TIMEOUT = 1     # Serial read timeout in seconds

class SensorDataCollector:
    def __init__(self):
        self.serial_port = None
        self.data_queue = queue.Queue()
        self.is_running = False
        self.thread = None
        
        # Data storage with deque for efficient append/pop operations
        self.data = {
            'timestamp': deque(maxlen=MAX_DATA_POINTS),
            'euler_x': deque(maxlen=MAX_DATA_POINTS),
            'euler_y': deque(maxlen=MAX_DATA_POINTS),
            'euler_z': deque(maxlen=MAX_DATA_POINTS),
            'gyro_x': deque(maxlen=MAX_DATA_POINTS),
            'gyro_y': deque(maxlen=MAX_DATA_POINTS),
            'gyro_z': deque(maxlen=MAX_DATA_POINTS),
            'accel_x': deque(maxlen=MAX_DATA_POINTS),
            'accel_y': deque(maxlen=MAX_DATA_POINTS),
            'accel_z': deque(maxlen=MAX_DATA_POINTS),
            'mag_x': deque(maxlen=MAX_DATA_POINTS),
            'mag_y': deque(maxlen=MAX_DATA_POINTS),
            'mag_z': deque(maxlen=MAX_DATA_POINTS),
            'linear_accel_x': deque(maxlen=MAX_DATA_POINTS),
            'linear_accel_y': deque(maxlen=MAX_DATA_POINTS),
            'linear_accel_z': deque(maxlen=MAX_DATA_POINTS),
            'gravity_x': deque(maxlen=MAX_DATA_POINTS),
            'gravity_y': deque(maxlen=MAX_DATA_POINTS),
            'gravity_z': deque(maxlen=MAX_DATA_POINTS),
            'temperature': deque(maxlen=MAX_DATA_POINTS),
            'system_cal': deque(maxlen=MAX_DATA_POINTS),
            'gyro_cal': deque(maxlen=MAX_DATA_POINTS),
            'accel_cal': deque(maxlen=MAX_DATA_POINTS),
            'mag_cal': deque(maxlen=MAX_DATA_POINTS),
        }
        
    def find_arduino_port(self):
        """Find the Arduino port automatically"""
        ports = serial.tools.list_ports.comports()
        for port in ports:
            # Look for common Arduino identifiers
            if any(keyword in port.description.lower() for keyword in 
                   ['arduino', 'ch340', 'cp210', 'ftdi', 'usb serial', 'xiao']):
                return port.device
        return None
    
    def connect(self, port=None, baudrate=115200):
        """Connect to the Arduino"""
        if port is None:
            port = self.find_arduino_port()
            if port is None:
                raise Exception("No Arduino found. Please specify port manually.")
        
        try:
            self.serial_port = serial.Serial(port, baudrate, timeout=SERIAL_TIMEOUT)
            time.sleep(2)  # Wait for Arduino to reset
            print(f"Connected to Arduino on {port}")
            return True
        except Exception as e:
            print(f"Failed to connect to {port}: {e}")
            return False
    
    def start_collection(self):
        """Start data collection in a separate thread"""
        if self.serial_port and not self.is_running:
            self.is_running = True
            self.thread = threading.Thread(target=self._collect_data)
            self.thread.daemon = True
            self.thread.start()
            print("Data collection started")
    
    def stop_collection(self):
        """Stop data collection"""
        self.is_running = False
        if self.thread:
            self.thread.join()
        if self.serial_port:
            self.serial_port.close()
        print("Data collection stopped")
    
    def _collect_data(self):
        """Data collection thread function"""
        while self.is_running:
            try:
                if self.serial_port.in_waiting > 0:
                    line = self.serial_port.readline().decode('utf-8').strip()
                    
                    # Skip header lines and comments
                    if line.startswith('#') or line.startswith('Timestamp') or not line:
                        continue
                    
                    # Parse CSV data
                    try:
                        values = [float(x) if '.' in x else int(x) for x in line.split(',')]
                        if len(values) == 24:  # Expected number of columns
                            # Store data
                            columns = ['timestamp', 'euler_x', 'euler_y', 'euler_z',
                                     'gyro_x', 'gyro_y', 'gyro_z',
                                     'accel_x', 'accel_y', 'accel_z',
                                     'mag_x', 'mag_y', 'mag_z',
                                     'linear_accel_x', 'linear_accel_y', 'linear_accel_z',
                                     'gravity_x', 'gravity_y', 'gravity_z',
                                     'temperature', 'system_cal', 'gyro_cal', 'accel_cal', 'mag_cal']
                            
                            for i, col in enumerate(columns):
                                self.data[col].append(values[i])
                    
                    except (ValueError, IndexError) as e:
                        print(f"Error parsing line: {line} - {e}")
                        continue
                        
            except Exception as e:
                print(f"Error reading serial data: {e}")
                time.sleep(0.1)
    
    def get_latest_data(self):
        """Get the latest data as a pandas DataFrame"""
        if not self.data['timestamp']:
            return pd.DataFrame()
        
        df_data = {}
        for key, values in self.data.items():
            df_data[key] = list(values)
        
        return pd.DataFrame(df_data)

# Initialize data collector
collector = SensorDataCollector()

# Initialize Dash app
app = dash.Dash(__name__)
app.title = "BNO055 Sensor Dashboard"

# Define the layout
app.layout = html.Div([
    html.H1("🚀 BNO055 Sensor Dashboard", 
            style={'textAlign': 'center', 'marginBottom': 30}),
    
    # Connection controls
    html.Div([
        html.Div([
            html.Label("Serial Port:"),
            dcc.Input(id='port-input', type='text', placeholder='Auto-detect', 
                     style={'marginLeft': 10, 'marginRight': 10}),
            html.Button('Connect', id='connect-btn', n_clicks=0,
                       style={'marginRight': 10}),
            html.Button('Disconnect', id='disconnect-btn', n_clicks=0),
            html.Div(id='connection-status', style={'marginTop': 10})
        ], style={'textAlign': 'center', 'marginBottom': 20})
    ]),
    
    # Auto-refresh component
    dcc.Interval(
        id='interval-component',
        interval=UPDATE_INTERVAL,
        n_intervals=0
    ),
    
    # Graphs
    html.Div([
        # Orientation (Euler angles)
        dcc.Graph(id='euler-graph'),
        
        # Gyroscope
        dcc.Graph(id='gyro-graph'),
        
        # Accelerometer
        dcc.Graph(id='accel-graph'),
        
        # Magnetometer
        dcc.Graph(id='mag-graph'),
        
        # Linear acceleration and gravity
        html.Div([
            dcc.Graph(id='linear-accel-graph', style={'width': '50%', 'display': 'inline-block'}),
            dcc.Graph(id='gravity-graph', style={'width': '50%', 'display': 'inline-block'})
        ]),
        
        # Temperature and calibration
        html.Div([
            dcc.Graph(id='temp-graph', style={'width': '50%', 'display': 'inline-block'}),
            dcc.Graph(id='calibration-graph', style={'width': '50%', 'display': 'inline-block'})
        ])
    ])
])

@app.callback(
    Output('connection-status', 'children'),
    [Input('connect-btn', 'n_clicks'),
     Input('disconnect-btn', 'n_clicks')],
    [dash.dependencies.State('port-input', 'value')]
)
def handle_connection(connect_clicks, disconnect_clicks, port):
    ctx = dash.callback_context
    if not ctx.triggered:
        return "Not connected"
    
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    if button_id == 'connect-btn' and connect_clicks > 0:
        try:
            if collector.connect(port):
                collector.start_collection()
                return html.Span("✅ Connected and collecting data", style={'color': 'green'})
            else:
                return html.Span("❌ Failed to connect", style={'color': 'red'})
        except Exception as e:
            return html.Span(f"❌ Error: {str(e)}", style={'color': 'red'})
    
    elif button_id == 'disconnect-btn' and disconnect_clicks > 0:
        collector.stop_collection()
        return html.Span("⏹️ Disconnected", style={'color': 'orange'})
    
    return "Not connected"

def create_time_series_graph(df, columns, title, y_label, colors=None):
    """Helper function to create time series graphs"""
    if df.empty:
        return {'data': [], 'layout': {'title': title, 'xaxis': {'title': 'Time'}, 'yaxis': {'title': y_label}}}
    
    if colors is None:
        colors = px.colors.qualitative.Set1
    
    traces = []
    for i, col in enumerate(columns):
        traces.append(go.Scatter(
            x=df['timestamp'],
            y=df[col],
            mode='lines',
            name=col,
            line=dict(color=colors[i % len(colors)])
        ))
    
    layout = go.Layout(
        title=title,
        xaxis={'title': 'Time (ms)'},
        yaxis={'title': y_label},
        hovermode='x unified'
    )
    
    return {'data': traces, 'layout': layout}

# Callback for updating all graphs
@app.callback(
    [Output('euler-graph', 'figure'),
     Output('gyro-graph', 'figure'),
     Output('accel-graph', 'figure'),
     Output('mag-graph', 'figure'),
     Output('linear-accel-graph', 'figure'),
     Output('gravity-graph', 'figure'),
     Output('temp-graph', 'figure'),
     Output('calibration-graph', 'figure')],
    [Input('interval-component', 'n_intervals')]
)
def update_graphs(n):
    df = collector.get_latest_data()
    
    # Euler angles (orientation)
    euler_fig = create_time_series_graph(
        df, ['euler_x', 'euler_y', 'euler_z'],
        'Orientation (Euler Angles)', 'Degrees'
    )
    
    # Gyroscope
    gyro_fig = create_time_series_graph(
        df, ['gyro_x', 'gyro_y', 'gyro_z'],
        'Gyroscope', 'rad/s'
    )
    
    # Accelerometer
    accel_fig = create_time_series_graph(
        df, ['accel_x', 'accel_y', 'accel_z'],
        'Accelerometer', 'm/s²'
    )
    
    # Magnetometer
    mag_fig = create_time_series_graph(
        df, ['mag_x', 'mag_y', 'mag_z'],
        'Magnetometer', 'µT'
    )
    
    # Linear acceleration
    linear_accel_fig = create_time_series_graph(
        df, ['linear_accel_x', 'linear_accel_y', 'linear_accel_z'],
        'Linear Acceleration', 'm/s²'
    )
    
    # Gravity
    gravity_fig = create_time_series_graph(
        df, ['gravity_x', 'gravity_y', 'gravity_z'],
        'Gravity Vector', 'm/s²'
    )
    
    # Temperature
    temp_fig = create_time_series_graph(
        df, ['temperature'],
        'Temperature', '°C'
    )
    
    # Calibration status
    cal_fig = create_time_series_graph(
        df, ['system_cal', 'gyro_cal', 'accel_cal', 'mag_cal'],
        'Calibration Status', 'Level (0-3)'
    )
    
    return euler_fig, gyro_fig, accel_fig, mag_fig, linear_accel_fig, gravity_fig, temp_fig, cal_fig

if __name__ == '__main__':
    print("🚀 Starting BNO055 Sensor Dashboard...")
    print("📊 Dashboard will be available at: http://127.0.0.1:8050")
    print("🔌 Make sure your Arduino is connected and running the BNO055 code")
    
    app.run_server(debug=True, host='127.0.0.1', port=8050)
