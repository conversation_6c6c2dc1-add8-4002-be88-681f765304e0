#include <bluefruit.h>
#include <Wire.h>

// BLE Service and Characteristic UUIDs (must match Python code)
uint8_t serviceUUID[16] = {0xbc, 0x9a, 0x78, 0x56, 0x34, 0x12, 0x34, 0x12, 0x34, 0x12, 0x34, 0x12, 0x78, 0x56, 0x34, 0x12};
uint8_t characteristicUUID[16] = {0x21, 0x43, 0x65, 0x87, 0xa9, 0xcb, 0x21, 0x43, 0x21, 0x43, 0x21, 0x43, 0x21, 0x43, 0x65, 0x87};

// BLE Service and Characteristic
BLEService sensorService = BLEService(serviceUUID);
BLECharacteristic sensorCharacteristic = BLECharacteristic(characteristicUUID);

// I2C communication from Feather M0
#define I2C_ADDRESS 0x42  // Must match Feather M0 address

// Data packet structure (matches Feather M0 output)
struct DataPacket {
  uint8_t header1;
  uint8_t header2;
  uint32_t timestamp;
  int16_t angular_velocity;
  int16_t angular_velocity_x;
  int16_t angular_velocity_y;
  int16_t angular_velocity_z;
  int16_t accel_x;
  int16_t accel_y;
  int16_t accel_z;
};

// Buffer for incoming I2C data
uint8_t i2cBuffer[20];
volatile bool packetReady = false;
volatile int bytesReceived = 0;

void setup() {
  Serial.begin(115200);  // Debug serial

  // Initialize I2C as slave
  Wire.begin(I2C_ADDRESS);
  Wire.onReceive(receiveI2CData);  // Register I2C receive handler

  // Initialize Bluefruit
  Bluefruit.begin();
  Bluefruit.setTxPower(4);    // Check bluefruit.h for supported values
  Bluefruit.setName("XIAO-SensorHub");

  // Configure and Start BLE Uart Service
  sensorService.begin();

  // Set up characteristics
  sensorCharacteristic.setProperties(CHR_PROPS_READ | CHR_PROPS_NOTIFY);
  sensorCharacteristic.setPermission(SECMODE_OPEN, SECMODE_NO_ACCESS);
  sensorCharacteristic.setFixedLen(20);
  sensorCharacteristic.begin();

  // Start advertising
  startAdv();

  Serial.println("✅ XIAO nRF52840 BLE Sensor Hub Ready!");
  Serial.println("I2C Slave address: 0x42");
  Serial.println("Waiting for BLE connections...");
}

void loop() {
  static unsigned long lastStatusTime = 0;
  static bool lastConnectionState = false;

  // Debug: Print connection status changes
  bool currentConnectionState = Bluefruit.connected();
  if (currentConnectionState != lastConnectionState) {
    if (currentConnectionState) {
      Serial.println("🔗 BLE Connected!");
    } else {
      Serial.println("❌ BLE Disconnected!");
    }
    lastConnectionState = currentConnectionState;
  }

  // Debug: Print status every 10 seconds
  if (millis() - lastStatusTime > 10000) {
    Serial.print("Status - BLE Connected: ");
    Serial.print(currentConnectionState ? "YES" : "NO");
    Serial.print(", Bytes Received: ");
    Serial.print(bytesReceived);
    Serial.print(", Packet Ready: ");
    Serial.println(packetReady ? "YES" : "NO");
    lastStatusTime = millis();
  }

  // Check if we have I2C data to send via BLE
  if (packetReady) {
    Serial.println("🎯 Packet ready flag is TRUE");
    if (currentConnectionState) {
      Serial.println("🔗 BLE is connected, calling sendDataViaBLE()");
      sendDataViaBLE();
    } else {
      Serial.println("📦 I2C data received but no BLE connection");
    }
    packetReady = false;
    bytesReceived = 0;
    Serial.println("🔄 Reset packet ready flag");
  }

  // Test: Send a test notification every 30 seconds if connected but no real data
  static unsigned long lastTestTime = 0;
  if (currentConnectionState && !packetReady && (millis() - lastTestTime > 30000)) {
    Serial.println("🧪 Sending test BLE notification...");
    uint8_t testData[20] = {0xAA, 0xAA, 0x00, 0x00, 0x00, 0x01, 0x00, 0x64, 0x00, 0x64, 0x00, 0x64, 0x00, 0x64, 0x03, 0xE8, 0x00, 0x00, 0x00, 0x00};
    bool testResult = sensorCharacteristic.notify(testData, 20);
    Serial.print("Test notification result: ");
    Serial.println(testResult ? "SUCCESS" : "FAILED");
    lastTestTime = millis();
  }

  delay(10); // Small delay to prevent overwhelming the BLE stack
}

// I2C receive event handler
void receiveI2CData(int numBytes) {
  Serial.print("📨 I2C data received: ");
  Serial.print(numBytes);
  Serial.println(" bytes");

  if (numBytes == 20) {
    // Read all 20 bytes into buffer
    for (int i = 0; i < 20; i++) {
      if (Wire.available()) {
        i2cBuffer[i] = Wire.read();
      }
    }

    bytesReceived = numBytes;
    packetReady = true;

    // Debug: Print first few bytes
    Serial.print("First 4 bytes: ");
    for (int i = 0; i < 4; i++) {
      Serial.print("0x");
      Serial.print(i2cBuffer[i], HEX);
      Serial.print(" ");
    }
    Serial.println();

    Serial.println("✅ Complete I2C packet received!");
  } else {
    // Wrong number of bytes, discard
    Serial.println("❌ Wrong number of bytes, discarding");
    while (Wire.available()) {
      Wire.read(); // Discard remaining bytes
    }
  }
}

void sendDataViaBLE() {
  Serial.println("📡 sendDataViaBLE() called");

  // Send the raw packet data via BLE
  if (Bluefruit.connected()) {
    Serial.println("🔗 BLE is connected, sending notification...");

    // Print raw buffer for debugging
    Serial.print("Raw I2C buffer: ");
    for (int i = 0; i < 20; i++) {
      Serial.print("0x");
      Serial.print(i2cBuffer[i], HEX);
      Serial.print(" ");
    }
    Serial.println();

    bool success = sensorCharacteristic.notify(i2cBuffer, 20);
    Serial.print("Notification result: ");
    Serial.println(success ? "SUCCESS" : "FAILED");

    // Debug: Print received data
    DataPacket* packet = (DataPacket*)i2cBuffer;
    Serial.print("Sent via BLE - Timestamp: ");
    Serial.print(packet->timestamp);
    Serial.print(", Angular Vel: ");
    Serial.print(packet->angular_velocity / 100.0);
    Serial.print(", Accel X: ");
    Serial.print(packet->accel_x / 100.0);
    Serial.print(", Y: ");
    Serial.print(packet->accel_y / 100.0);
    Serial.print(", Z: ");
    Serial.println(packet->accel_z / 100.0);
  } else {
    Serial.println("❌ BLE not connected, cannot send notification");
  }
}

// Function to check BLE connection status
bool isBLEConnected() {
  return Bluefruit.connected();
}

// Function to get signal strength (if needed for debugging)
int getBLESignalStrength() {
  if (Bluefruit.connected()) {
    // For Adafruit Bluefruit nRF52, RSSI is not easily accessible
    // Return a placeholder value or remove this function if not needed
    return -50; // Placeholder RSSI value
  }
  return 0;
}

// Start advertising
void startAdv(void) {
  // Advertising packet
  Bluefruit.Advertising.addFlags(BLE_GAP_ADV_FLAGS_LE_ONLY_GENERAL_DISC_MODE);
  Bluefruit.Advertising.addTxPower();

  // Include the BLE UART service
  Bluefruit.Advertising.addService(sensorService);

  // Secondary Scan Response packet (optional)
  Bluefruit.ScanResponse.addName();

  /* Start Advertising
   * - Enable auto advertising if disconnected
   * - Interval:  fast mode = 20 ms, slow mode = 152.5 ms
   * - Timeout for fast mode is 30 seconds
   * - Start(timeout) with timeout = 0 will advertise forever (until connected)
   *
   * For recommended advertising interval
   * https://developer.apple.com/library/content/qa/qa1931/_index.html
   */
  Bluefruit.Advertising.restartOnDisconnect(true);
  Bluefruit.Advertising.setInterval(32, 244);    // in unit of 0.625 ms
  Bluefruit.Advertising.setFastTimeout(30);      // number of seconds in fast mode
  Bluefruit.Advertising.start(0);                // 0 = Don't stop advertising after n seconds
}
